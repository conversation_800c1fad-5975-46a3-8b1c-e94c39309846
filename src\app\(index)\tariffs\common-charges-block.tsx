import React from 'react';
import { Box, IconButton, Select, MenuItem, FormControl, SelectChangeEvent } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import { PlainTextField } from '../../../new-main-page-2-components';

type CommonCharge = {
  id: string;
  type: string;
  unit: string;
  rate: number;
  total: number;
  currency: string;
};

type CommonChargesBlockProps = {
  charges: CommonCharge[];
  onAddCharge: () => void;
  onDeleteCharge: (chargeId: string) => void;
  onUpdateCharge: (chargeId: string, field: keyof CommonCharge, value: any) => void;
};

const chargeTypes = [
  'Custom Clearance',
  'Fuel surcharge',
  'Security fee',
  'Terminal handling',
  'Documentation fee',
  'Insurance',
  'Storage fee'
];

const unitTypes = [
  'per kg',
  'per shipment',
  'per container',
  'per m³',
  'per piece'
];

// Simple select component without label for the charges block
const SimpleSelect: React.FC<{
  value: string;
  onChange: (value: string) => void;
  options: string[];
  placeholder?: string;
  sx?: any;
}> = ({ value, onChange, options, placeholder, sx }) => (
  <FormControl size="small" fullWidth>
    <Select
      value={value}
      onChange={(e: SelectChangeEvent) => onChange(e.target.value as string)}
      displayEmpty
      sx={{
        height: '38px',
        backgroundColor: '#ffffff',
        border: '1px solid #e6e7e8',
        borderRadius: '0px',
        fontSize: '12px',
        fontFamily: "'YS Text', sans-serif",
        '& .MuiOutlinedInput-notchedOutline': {
          border: 'none'
        },
        '& .MuiSelect-select': {
          padding: '10px 12px'
        },
        '&:hover': {
          border: '1px solid #d0d1d2'
        },
        '&.Mui-focused': {
          border: '1px solid #70B57D'
        },
        ...sx
      }}
    >
      {placeholder && (
        <MenuItem value="" disabled>
          <em style={{ color: '#9aa0a6' }}>{placeholder}</em>
        </MenuItem>
      )}
      {options.map((option) => (
        <MenuItem key={option} value={option} sx={{ fontSize: '12px' }}>
          {option}
        </MenuItem>
      ))}
    </Select>
  </FormControl>
);

export const CommonChargesBlock: React.FC<CommonChargesBlockProps> = ({
  charges,
  onAddCharge,
  onDeleteCharge,
  onUpdateCharge
}) => {
  return (
    <Box sx={{
      backgroundColor: '#ffffff',
      borderRadius: '14px',
      padding: '20px',
      boxShadow: '0px 2px 6px rgba(198, 197, 202, 0.55)',
      border: '1px solid #efefef',
      width: '100%',
      fontFamily: "'YS Text', sans-serif"
    }}>
      {/* Header Row */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: '2fr 0.8fr 1fr 1fr 0.8fr 40px',
        gap: '5px',
        marginBottom: '8px',
        alignItems: 'center'
      }}>
        <Box sx={{
          fontSize: '12px',
          fontWeight: 700,
          color: '#9aa0a6',
          textTransform: 'uppercase',
          letterSpacing: '0.5px',
          fontFamily: "'YS Text', sans-serif"
        }}>
          TYPE
        </Box>
        <Box sx={{
          fontSize: '12px',
          fontWeight: 700,
          color: '#9aa0a6',
          textTransform: 'uppercase',
          letterSpacing: '0.5px',
          fontFamily: "'YS Text', sans-serif"
        }}>
          UNIT
        </Box>
        <Box sx={{
          fontSize: '12px',
          fontWeight: 700,
          color: '#9aa0a6',
          textTransform: 'uppercase',
          letterSpacing: '0.5px',
          fontFamily: "'YS Text', sans-serif"
        }}>
          RATE
        </Box>
        <Box sx={{
          fontSize: '12px',
          fontWeight: 700,
          color: '#9aa0a6',
          textTransform: 'uppercase',
          letterSpacing: '0.5px',
          fontFamily: "'YS Text', sans-serif"
        }}>
          TOTAL
        </Box>
        <Box sx={{
          fontSize: '12px',
          fontWeight: 700,
          color: '#9aa0a6',
          textTransform: 'uppercase',
          letterSpacing: '0.5px',
          fontFamily: "'YS Text', sans-serif"
        }}>
          CURRENCY
        </Box>
        <Box /> {/* Empty space for delete button column */}
      </Box>

      {/* Charge Rows */}
      {charges.map((charge) => (
        <Box
          key={charge.id}
          sx={{
            display: 'grid',
            gridTemplateColumns: '2fr 0.8fr 1fr 1fr 0.8fr 40px',
            gap: '5px',
            marginBottom: '5px',
            alignItems: 'center',
            padding: '2px',
            borderRadius: '6px',
            transition: 'background-color 0.2s ease',
            '&:hover': {
              backgroundColor: '#f9f9f9'
            }
          }}
        >
          {/* Type Select */}
          <SimpleSelect
            value={charge.type}
            onChange={(value) => onUpdateCharge(charge.id, 'type', value)}
            options={chargeTypes}
            placeholder="Select type"
          />

          {/* Unit Select */}
          <SimpleSelect
            value={charge.unit}
            onChange={(value) => onUpdateCharge(charge.id, 'unit', value)}
            options={unitTypes}
            placeholder="Unit"
          />

          {/* Rate Input */}
          <PlainTextField
            value={charge.rate === 0 ? '00000' : String(charge.rate)}
            onChange={(e) => onUpdateCharge(charge.id, 'rate', Number(e.target.value) || 0)}
            type="text"
            additionalStyling={{
              '& .MuiOutlinedInput-root': {
                height: '38px',
                backgroundColor: '#ffffff',
                border: '1px solid #e6e7e8',
                borderRadius: '0px',
                fontSize: '12px',
                fontFamily: 'monospace',
                letterSpacing: '1px',
                '& fieldset': {
                  border: 'none'
                },
                '& input': {
                  textAlign: 'center',
                  padding: '10px 12px'
                },
                '&:hover': {
                  border: '1px solid #d0d1d2'
                },
                '&.Mui-focused': {
                  border: '1px solid #70B57D'
                }
              }
            }}
          />

          {/* Total Input */}
          <PlainTextField
            value={charge.total === 0 ? '00000' : String(charge.total)}
            onChange={(e) => onUpdateCharge(charge.id, 'total', Number(e.target.value) || 0)}
            type="text"
            additionalStyling={{
              '& .MuiOutlinedInput-root': {
                height: '38px',
                backgroundColor: '#ffffff',
                border: '1px solid #e6e7e8',
                borderRadius: '0px',
                fontSize: '12px',
                fontFamily: 'monospace',
                letterSpacing: '1px',
                '& fieldset': {
                  border: 'none'
                },
                '& input': {
                  textAlign: 'center',
                  padding: '10px 12px'
                },
                '&:hover': {
                  border: '1px solid #d0d1d2'
                },
                '&.Mui-focused': {
                  border: '1px solid #70B57D'
                }
              }
            }}
          />

          {/* Currency TextField */}
          <PlainTextField
            value={charge.currency}
            onChange={(e) => onUpdateCharge(charge.id, 'currency', e.target.value)}
            type="text"
            additionalStyling={{
              '& .MuiOutlinedInput-root': {
                height: '38px',
                backgroundColor: '#ffffff',
                border: '1px solid #e6e7e8',
                borderRadius: '0px',
                fontSize: '12px',
                fontFamily: "'YS Text', sans-serif",
                '& fieldset': {
                  border: 'none'
                },
                '& input': {
                  textAlign: 'center',
                  padding: '10px 12px'
                },
                '&:hover': {
                  border: '1px solid #d0d1d2'
                },
                '&.Mui-focused': {
                  border: '1px solid #70B57D'
                }
              }
            }}
          />

          {/* Delete Button */}
          <IconButton
            onClick={() => onDeleteCharge(charge.id)}
            sx={{
              width: '28px',
              height: '28px',
              backgroundColor: '#ff5c5c',
              color: '#ffffff',
              borderRadius: '50%',
              '&:hover': {
                backgroundColor: '#ff4444',
                transform: 'scale(0.95)'
              },
              '&:active': {
                transform: 'scale(0.9)'
              }
            }}
          >
            <CloseIcon sx={{ fontSize: '16px' }} />
          </IconButton>
        </Box>
      ))}

      {/* Add Charge Button */}
      <Box sx={{ marginTop: '8px' }}>
        <Box
          component="button"
          onClick={onAddCharge}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            backgroundColor: 'transparent',
            border: 'none',
            color: '#1a1a1a',
            fontSize: '12px',
            fontWeight: 700,
            fontFamily: "'YS Text', sans-serif",
            cursor: 'pointer',
            padding: '4px 0',
            '&:hover': {
              color: '#70B57D'
            }
          }}
        >
          <AddIcon sx={{ fontSize: '16px' }} />
          Add charge
        </Box>
      </Box>
    </Box>
  );
};
