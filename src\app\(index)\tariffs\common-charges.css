/* Frame 580678481 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

position: absolute;
width: 455px;
height: 161px;
left: 228px;
top: 546px;



/* Frame 580678467 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 455px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 580678459 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 140px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Type */

width: 60px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678461 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 70px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Unit */

width: 76px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Rate */

width: 60px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 580678462 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Total */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678478 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 440px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 140px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 128px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 124px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Placeholder(Replace) */

width: 124px;
height: 24px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 70px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 76px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* cancel */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.33%;

background: #EB4E3D;
border: 1px solid #FFFFFF;


/* Frame 580678481 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 440px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 140px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 128px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 124px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Placeholder(Replace) */

width: 124px;
height: 24px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 70px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 76px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* cancel */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.33%;

background: #EB4E3D;
border: 1px solid #FFFFFF;


/* Frame 580678482 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 440px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 140px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 128px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 124px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Placeholder(Replace) */

width: 124px;
height: 24px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 70px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 76px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* cancel */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.33%;

background: #EB4E3D;
border: 1px solid #FFFFFF;


/* Label(Replace) */

width: 455px;
height: 25px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: flex-end;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;